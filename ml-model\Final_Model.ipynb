{"cells": [{"cell_type": "code", "execution_count": 22, "id": "7b9a88a3-6f9d-4ac4-ad09-c9da8d2057d4", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.metrics import classification_report, accuracy_score, roc_auc_score\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": 7, "id": "c89f2ecb-947d-4f95-9492-97a8884dc7f3", "metadata": {}, "outputs": [], "source": ["# Load the data\n", "df = pd.read_csv('synthetic_health_data_15000.csv')\n", "\n", "# Convert sleep to total minutes\n", "df['total_sleep_minutes'] = df['sleep_hours'] * 60 + df['sleep_minutes']\n", "df.drop(['sleep_hours', 'sleep_minutes'], axis=1, inplace=True)\n", "\n", "# Define features and labels\n", "features = ['step_count', 'calories', 'total_sleep_minutes', 'bmi', 'heart_rate_bpm', 'spo2', 'stress_level']\n", "labels = ['Obesity', 'Hypertension', 'Type2Diabetes', 'CVD', 'Anxiety', 'Insomnia']\n", "\n", "X = df[features]\n", "y = df[labels]\n"]}, {"cell_type": "code", "execution_count": 9, "id": "0ac710af-81e1-412e-9fc6-d9a4eeb348f0", "metadata": {}, "outputs": [], "source": ["# Scale features\n", "scaler = MinMaxScaler()\n", "X_scaled = scaler.fit_transform(X)\n", "\n", "# Reshape for LSTM input: (samples, time_steps=1, features)\n", "X_lstm = X_scaled.reshape((X_scaled.shape[0], 1, X_scaled.shape[1]))\n", "\n", "# Train-test split\n", "X_train, X_test, y_train, y_test = train_test_split(X_lstm, y, test_size=0.2, random_state=42)\n"]}, {"cell_type": "code", "execution_count": 23, "id": "8c73dc13-0484-472e-b3b9-4e7df6358ffa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m14s\u001b[0m 24ms/step - accuracy: 0.3713 - loss: 0.5290 - val_accuracy: 0.6958 - val_loss: 0.3604\n", "Epoch 2/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 8ms/step - accuracy: 0.4446 - loss: 0.3471 - val_accuracy: 0.5783 - val_loss: 0.2419\n", "Epoch 3/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 8ms/step - accuracy: 0.5711 - loss: 0.2334 - val_accuracy: 0.5917 - val_loss: 0.1791\n", "Epoch 4/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 7ms/step - accuracy: 0.5760 - loss: 0.2012 - val_accuracy: 0.6292 - val_loss: 0.1713\n", "Epoch 5/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 9ms/step - accuracy: 0.5864 - loss: 0.1866 - val_accuracy: 0.6096 - val_loss: 0.1597\n", "Epoch 6/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m2s\u001b[0m 7ms/step - accuracy: 0.5943 - loss: 0.1818 - val_accuracy: 0.6150 - val_loss: 0.1556\n", "Epoch 7/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 8ms/step - accuracy: 0.6057 - loss: 0.1777 - val_accuracy: 0.5808 - val_loss: 0.1516\n", "Epoch 8/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 7ms/step - accuracy: 0.6063 - loss: 0.1687 - val_accuracy: 0.5913 - val_loss: 0.1469\n", "Epoch 9/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 8ms/step - accuracy: 0.6302 - loss: 0.1640 - val_accuracy: 0.6429 - val_loss: 0.1430\n", "Epoch 10/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 8ms/step - accuracy: 0.6231 - loss: 0.1591 - val_accuracy: 0.6650 - val_loss: 0.1373\n", "Epoch 11/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 7ms/step - accuracy: 0.6660 - loss: 0.1579 - val_accuracy: 0.6825 - val_loss: 0.1316\n", "Epoch 12/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 7ms/step - accuracy: 0.6746 - loss: 0.1545 - val_accuracy: 0.7088 - val_loss: 0.1316\n", "Epoch 13/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 7ms/step - accuracy: 0.6953 - loss: 0.1486 - val_accuracy: 0.7304 - val_loss: 0.1261\n", "Epoch 14/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m2s\u001b[0m 9ms/step - accuracy: 0.6938 - loss: 0.1476 - val_accuracy: 0.7296 - val_loss: 0.1227\n", "Epoch 15/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 8ms/step - accuracy: 0.7081 - loss: 0.1430 - val_accuracy: 0.7396 - val_loss: 0.1199\n", "Epoch 16/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 8ms/step - accuracy: 0.7268 - loss: 0.1378 - val_accuracy: 0.7387 - val_loss: 0.1127\n", "Epoch 17/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 7ms/step - accuracy: 0.7107 - loss: 0.1312 - val_accuracy: 0.7575 - val_loss: 0.1056\n", "Epoch 18/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 8ms/step - accuracy: 0.7164 - loss: 0.1256 - val_accuracy: 0.7592 - val_loss: 0.0973\n", "Epoch 19/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 9ms/step - accuracy: 0.7147 - loss: 0.1192 - val_accuracy: 0.7450 - val_loss: 0.0915\n", "Epoch 20/20\n", "\u001b[1m150/150\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 8ms/step - accuracy: 0.7152 - loss: 0.1129 - val_accuracy: 0.7633 - val_loss: 0.0864\n"]}], "source": ["import tensorflow as tf\n", "from tensorflow.keras.models import Sequential\n", "from tensorflow.keras.layers import LSTM, Dense, Dropout\n", "from tensorflow.keras.callbacks import EarlyStopping\n", "\n", "# Build model\n", "from tensorflow.keras.layers import Bidirectional\n", "model = Sequential([\n", "    Bidirectional(LSTM(64, return_sequences=True), input_shape=(1, X_lstm.shape[2])),\n", "    Dropout(0.3),\n", "    Bidirectional(LSTM(32)),\n", "    <PERSON><PERSON>(64, activation='relu'),\n", "    Dropout(0.3),\n", "    Dense(len(labels), activation='sigmoid')\n", "])\n", "\n", "\n", "model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])\n", "\n", "# Train\n", "history = model.fit(X_train, y_train, epochs=20, batch_size=64, validation_split=0.2,\n", "                    callbacks=[EarlyStopping(patience=3, restore_best_weights=True)])\n"]}, {"cell_type": "code", "execution_count": 18, "id": "4f88bba5-0bf5-471d-91dc-e58102474ac5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m94/94\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 17ms/step\n", "               precision    recall  f1-score   support\n", "\n", "      Obesity       0.84      0.74      0.79       790\n", " Hypertension       0.98      0.96      0.97       740\n", "Type2Diabetes       0.71      0.59      0.64       173\n", "          CVD       0.54      0.18      0.27        79\n", "      Anxiety       0.96      0.81      0.88       269\n", "     Insomnia       0.90      0.89      0.90       708\n", "\n", "    micro avg       0.90      0.82      0.86      2759\n", "    macro avg       0.82      0.69      0.74      2759\n", " weighted avg       0.89      0.82      0.85      2759\n", "  samples avg       0.50      0.48      0.48      2759\n", "\n", "Accuracy: 0.78\n", "ROC AUC (macro): 0.9782\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from sklearn.metrics import classification_report, roc_auc_score, accuracy_score\n", "import numpy as np\n", "\n", "# Predict on test data\n", "y_pred_prob = model.predict(X_test)\n", "y_pred_bin = (y_pred_prob > 0.5).astype(int)\n", "\n", "# Classification Report\n", "print(classification_report(y_test, y_pred_bin, target_names=labels,zero_division=0))\n", "\n", "# Accuracy\n", "print(\"Accuracy:\", accuracy_score(y_test, y_pred_bin))\n", "\n", "# ROC AUC score (filtered for valid labels)\n", "valid = [i for i in range(y_test.shape[1]) if len(np.unique(y_test.iloc[:, i])) == 2]\n", "if valid:\n", "    auc = roc_auc_score(y_test.iloc[:, valid], y_pred_prob[:, valid], average='macro')\n", "    print(\"ROC AUC (macro):\", round(auc, 4))\n", "else:\n", "    print(\"Not enough label diversity to calculate ROC AUC.\")\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import roc_curve, auc\n", "\n", "# Compute ROC curve and AUC for each label\n", "fpr = {}\n", "tpr = {}\n", "roc_auc = {}\n", "\n", "for i in range(y_test.shape[1]):\n", "    fpr[i], tpr[i], _ = roc_curve(y_test.values[:, i], y_pred_prob[:, i])\n", "    roc_auc[i] = auc(fpr[i], tpr[i])\n", "\n", "# Plot ROC curves\n", "plt.figure(figsize=(10, 7))\n", "for i in range(len(labels)):\n", "    plt.plot(fpr[i], tpr[i], lw=2, label=f'{labels[i]} (AUC = {roc_auc[i]:.2f})')\n", "\n", "plt.plot([0, 1], [0, 1], 'k--', lw=2)\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "plt.title('ROC Curve for Each Symptom')\n", "plt.legend(loc='lower right')\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 8, "id": "0c4296f5-ca40-4c23-88b1-422d7862504e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['step_count', 'calories', 'bmi', 'heart_rate_bpm', 'spo2', 'stress_level', 'Obesity', 'Hypertension', 'Type2Diabetes', 'CVD', 'Anxiety', 'Insomnia', 'total_sleep_minutes']\n"]}], "source": ["print(df.columns.tolist())"]}, {"cell_type": "code", "execution_count": 24, "id": "0d60a35f-5122-4c78-980a-d8ce7aa38ae8", "metadata": {}, "outputs": [], "source": ["def predict_manual(step_count, calories, sleep_hours, sleep_minutes, bmi, heart_rate_bpm, spo2, stress_level):\n", "    total_sleep_minutes = sleep_hours * 60 + sleep_minutes\n", "    input_array = np.array([[step_count, calories, total_sleep_minutes, bmi, heart_rate_bpm, spo2, stress_level]])\n", "    input_scaled = scaler.transform(input_array)\n", "    input_lstm = input_scaled.reshape((1, 1, input_scaled.shape[1]))\n", "    prob = model.predict(input_lstm)\n", "    pred = (prob > 0.5).astype(int)\n", "    \n", "    for i, label in enumerate(labels):\n", "        result = \"Positive\" if pred[0][i] else \"Negative\"\n", "        print(f\"{label}: {result}\")\n"]}, {"cell_type": "code", "execution_count": 25, "id": "9195352b-9ae1-4584-b00e-0e16d804ac9d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 1s/step\n", "Obesity: Positive\n", "Hypertension: Positive\n", "Type2Diabetes: Positive\n", "CVD: Negative\n", "Anxiety: Negative\n", "Insomnia: Positive\n"]}], "source": ["predict_manual(1000, 200, 3, 0, 34.5, 108, 86, 30)\n"]}, {"cell_type": "code", "execution_count": 27, "id": "337ae901-1225-4cac-afd5-9f1c57a11f18", "metadata": {}, "outputs": [], "source": ["model.save(\"health_model.keras\")\n", "import pickle\n", "with open('scaler.pkl', 'wb') as f:\n", "    pickle.dump(scaler, f)\n"]}, {"cell_type": "code", "execution_count": null, "id": "75003e3c-acf7-491a-9893-f2f144a4d781", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}